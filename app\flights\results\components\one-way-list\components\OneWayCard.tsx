// 'use client';

// import React, { useState } from 'react';
// import { FlightOptionOneWay } from '@/app/models/flight-list.model';
// import { getAirlineLogo } from '@/app/utils/airlineUtils';


// interface OneWayCardProps {
//   flight: FlightOptionOneWay;
//   onSelect?: (flight: FlightOptionOneWay) => void;
// }

// export default function OneWayCard({ flight, onSelect }: OneWayCardProps) {
//   const [showDetails, setShowDetails] = useState(false);

//   // Extract flight data from the API structure
//   const segments = flight.SegmentInformation || [];
//   const firstSegment = segments[0] || {};
//   const lastSegment = segments[segments.length - 1] || {};
//   const fareInfo = flight.totalPriceList?.[0] || {};

//   // Calculate total duration
//   const totalDuration = segments.reduce((total: number, segment: any) => {
//     const duration = segment.duration || 0;
//     return total + duration;
//   }, 0);

//   const formatDuration = (minutes: number) => {
//     const hours = Math.floor(minutes / 60);
//     const mins = minutes % 60;
//     return `${hours}h ${mins}m`;
//   };

//   const formatTime = (dateTime: string) => {
//     if (!dateTime) return '--:--';
//     try {
//       const date = new Date(dateTime);
//       return date.toLocaleTimeString('en-US', {
//         hour: '2-digit',
//         minute: '2-digit',
//         hour12: false
//       });
//     } catch {
//       return '--:--';
//     }
//   };

//   const handleImageError = (e: React.SyntheticEvent<HTMLImageElement>) => {
//     const target = e.target as HTMLImageElement;
//     target.src = '/AirlineLogo/All.png';
//   };

//   // Get fare information
//   const adultFare = fareInfo.FareDetail?.ADULT;
//   const totalFare = adultFare?.fareComponents?.TotalFare || 0;

//   return (
//     <div className="relative">
//       <div className="bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-all p-4">
//       {/* Airline Info Header - Match Old FlightCard */}
//       <div className="flex items-center justify-between mb-3">
//         <div className="flex items-center space-x-3">
//           <div className="flex-shrink-0">
//             <img
//               src={getAirlineLogo(firstSegment.FlightDesignator?.MAC?.code)}
//               alt={firstSegment.FlightDesignator?.MAC?.name || 'Airline'}
//               className="w-10 h-6 object-contain"
//               onError={handleImageError}
//             />
//           </div>
//           <div>
//             <div className="font-semibold text-gray-900 text-sm">
//               {firstSegment.FlightDesignator?.MAC?.name || 'Unknown Airline'}
//             </div>
//             <div className="text-gray-500 text-xs">
//               {firstSegment.FlightDesignator?.FlightNumber || 'N/A'}
//             </div>
//           </div>
//         </div>
//       </div>

//       {/* Flight Times and Duration - Match Old FlightCard 5-column Grid */}
//       <div className="grid grid-cols-5 gap-2 items-center mb-3">
//         {/* Departure */}
//         <div className="text-left">
//           <div className="font-bold text-gray-900 text-xl">{formatTime(firstSegment.DepartureTime)}</div>
//           <div className="text-gray-600 text-sm">{firstSegment.DepartureAirport?.city}</div>
//           <div className="text-gray-500 text-xs">{firstSegment.DepartureAirport?.code}</div>
//         </div>

//         {/* Duration and Route */}
//         <div className="col-span-3 text-center">
//           <div className="text-gray-600 text-sm mb-1">{formatDuration(totalDuration)}</div>
//           <div className="flex items-center justify-center mb-1">
//             <div className="w-2 h-2 bg-[#013688] rounded-full"></div>
//             <div className="flex-1 h-px bg-gray-300 mx-2 relative">
//               <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
//                 <i className="ri-plane-line text-[#013688] text-sm"></i>
//               </div>
//             </div>
//             <div className="w-2 h-2 bg-[#013688] rounded-full"></div>
//           </div>
//           <div className="text-gray-500 text-xs">
//             {segments.length > 1 ? `${segments.length - 1} stop${segments.length > 2 ? 's' : ''}` : 'Non-stop'}
//           </div>
//         </div>

//         {/* Arrival */}
//         <div className="text-right">
//           <div className="font-bold text-gray-900 text-xl">{formatTime(lastSegment.Arrivaltime)}</div>
//           <div className="text-gray-600 text-sm">{lastSegment.ArrivalAirport?.city}</div>
//           <div className="text-gray-500 text-xs">{lastSegment.ArrivalAirport?.code}</div>
//         </div>
//       </div>

//       {/* Price and Action - Match Old FlightCard Bottom Section */}
//       <div className="flex items-center justify-between pt-3 border-t border-gray-100">
//         <div className="flex items-center space-x-4">
//           <div>
//             <div className="flex items-center space-x-2">
//               <span className="font-bold text-gray-900 text-xl">₹{totalFare.toLocaleString()}</span>
//             </div>
//           </div>
//           <div className="text-sm text-gray-600">
//             <div className="flex items-center space-x-1">
//               <i className="ri-luggage-cart-line text-xs"></i>
//               <span>15 kg</span>
//             </div>
//           </div>
//         </div>

//         <div className="flex items-center space-x-3">
//           <button
//             onClick={() => setShowDetails(!showDetails)}
//             className="text-[#013688] font-semibold hover:text-blue-700 transition-colors text-sm"
//           >
//             {showDetails ? 'Hide Details' : 'Flight Details'}
//           </button>
//           <button
//             onClick={() => onSelect?.(flight)}
//             className="bg-[#013688] text-white px-4 py-2 rounded-lg font-semibold hover:bg-blue-700 transition-all text-sm"
//           >
//             Select
//           </button>
//         </div>
//       </div>

//       {/* Expandable Details */}
//       {showDetails && (
//         <div className="mt-4 pt-4 border-t border-gray-200">
//           <h4 className="font-semibold mb-2">Flight Details</h4>
//           {segments.map((segment: any, index: number) => (
//             <div key={index} className="mb-3 last:mb-0">
//               <div className="flex justify-between items-center">
//                 <div>
//                   <div className="font-medium">
//                     {segment.DepartureAirport?.city} → {segment.ArrivalAirport?.city}
//                   </div>
//                   <div className="text-sm text-gray-600">
//                     {segment.FlightDesignator?.MAC?.name} • {segment.FlightDesignator?.FlightNumber}
//                   </div>
//                 </div>
//                 <div className="text-right text-sm">
//                   <div>{formatTime(segment.DepartureTime)} - {formatTime(segment.Arrivaltime)}</div>
//                   <div className="text-gray-500">{formatDuration(segment.duration || 0)}</div>
//                 </div>
//               </div>
//             </div>
//           ))}

//           {/* Fare Rules */}
//           <div className="mt-3 pt-3 border-t border-gray-100">
//             <h5 className="font-medium text-sm mb-2">Fare Rules</h5>
//             <ul className="text-xs text-gray-600 space-y-1">
//               <li>• Cancellation charges apply as per airline policy</li>
//               <li>• Date change allowed with charges</li>
//               <li>• No refund on no-show</li>
//               <li>• Baggage allowance: 15kg check-in + 7kg cabin</li>
//             </ul>
//           </div>
//         </div>
//       )}
//       </div>
//     </div>
//   );
// }

'use client';

import React, { useState } from 'react';
import { FlightOptionOneWay } from '@/app/models/flight-list.model';
import { getAirlineLogo } from '@/app/utils/airlineUtils';

interface OneWayCardProps {
  flight: FlightOptionOneWay;
  compact?: boolean;
  selected?: boolean;
  onSelect?: (flight: FlightOptionOneWay) => void;
}

export default function OneWayCard({ flight, compact = false, selected = false, onSelect }: OneWayCardProps) {
  const [showDetails, setShowDetails] = useState(false);
  const [showFlightDetails, setShowFlightDetails] = useState(false);
  const [activeTab, setActiveTab] = useState('details');
  const [activeDetailsTab, setActiveDetailsTab] = useState('flight');
  const [activeFareSubTab, setActiveFareSubTab] = useState('changes');

  // Extract flight data from the API structure
  const segments = flight.SegmentInformation || [];
  const firstSegment = segments[0] || {};
  const lastSegment = segments[segments.length - 1] || {};
  const fareInfo = flight.totalPriceList?.[0] || {};

  // Calculate total duration
  const totalDuration = segments.reduce((total: number, segment: any) => {
    const duration = segment.duration || 0;
    return total + duration;
  }, 0);

  const formatDuration = (minutes: number) => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return `${hours}h ${mins}m`;
  };

  const formatTime = (dateTime: string) => {
    if (!dateTime) return '--:--';
    try {
      const date = new Date(dateTime);
      return date.toLocaleTimeString('en-US', {
        hour: '2-digit',
        minute: '2-digit',
        hour12: false
      });
    } catch {
      return '--:--';
    }
  };

  const handleImageError = (e: React.SyntheticEvent<HTMLImageElement>) => {
    const target = e.target as HTMLImageElement;
    target.src = '/AirlineLogo/All.png';
  };

  const handleShowFlightDetails = () => {
    setShowFlightDetails(true);
    setActiveTab('details');
  };

  const handleCloseFlightDetails = () => {
    setShowFlightDetails(false);
  };

  const handleBookNow = () => {
    onSelect?.(flight);
  };

  // Get fare information
  const adultFare = fareInfo.FareDetail?.ADULT;
  const totalFare = adultFare?.fareComponents?.TotalFare || 0;
  const baseFare = adultFare?.fareComponents?.BaseFare || 0;
  const taxes = adultFare?.fareComponents?.Tax || 0;

  return (
    <div className="relative">
      <div className={`bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-all ${selected ? 'border-[#013688] bg-blue-50' : ''} ${compact ? 'p-3' : 'p-4'}`}>
        {/* Trip Type Badge */}
        <div className="mb-2">
          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
            One Way • Domestic
          </span>
        </div>

        {/* Main Flight Info - Compact Header */}
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center space-x-3">
            <div className="flex-shrink-0">
              <img
                src={getAirlineLogo(firstSegment.FlightDesignator?.MAC?.code)}
                alt={firstSegment.FlightDesignator?.MAC?.name || 'Airline'}
                className="w-10 h-6 object-contain"
                onError={handleImageError}
              />
            </div>
            <div>
              <div className="font-semibold text-gray-900 text-sm">
                {firstSegment.FlightDesignator?.MAC?.name || 'Unknown Airline'}
              </div>
              <div className="text-gray-500 text-xs">
                {firstSegment.FlightDesignator?.FlightNumber || 'N/A'}
              </div>
            </div>
          </div>
        </div>

        {/* Flight Times and Duration - Compact Layout */}
        <div className="grid grid-cols-5 gap-2 items-center mb-3">
          {/* Departure */}
          <div className="text-left">
            <div className="font-bold text-gray-900 text-xl">{formatTime(firstSegment.DepartureTime)}</div>
            <div className="text-gray-600 text-sm">{firstSegment.DepartureAirport?.city}</div>
            <div className="text-gray-500 text-xs">{firstSegment.DepartureAirport?.code}</div>
          </div>

          {/* Duration and Route */}
          <div className="col-span-3 text-center">
            <div className="text-gray-600 text-sm mb-1">{formatDuration(totalDuration)}</div>
            <div className="flex items-center justify-center mb-1">
              <div className="w-2 h-2 bg-[#013688] rounded-full"></div>
              <div className="flex-1 h-px bg-gray-300 mx-2 relative">
                <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                  <i className="ri-plane-line text-[#013688] text-sm"></i>
                </div>
              </div>
              <div className="w-2 h-2 bg-[#013688] rounded-full"></div>
            </div>
            <div className="text-gray-500 text-xs">
              {segments.length > 1 ? `${segments.length - 1} stop${segments.length > 2 ? 's' : ''}` : 'Non-stop'}
            </div>
          </div>

          {/* Arrival */}
          <div className="text-right">
            <div className="font-bold text-gray-900 text-xl">{formatTime(lastSegment.Arrivaltime)}</div>
            <div className="text-gray-600 text-sm">{lastSegment.ArrivalAirport?.city}</div>
            <div className="text-gray-500 text-xs">{lastSegment.ArrivalAirport?.code}</div>
          </div>
        </div>

        {/* Price and Action - Compact Bottom Section */}
        <div className="flex items-center justify-between pt-3 border-t border-gray-100">
          <div className="flex items-center space-x-4">
            <div>
              <div className="flex items-center space-x-2">
                <span className="font-bold text-gray-900 text-xl">₹{totalFare.toLocaleString()}</span>
              </div>
            </div>
            <div className="text-sm text-gray-600">
              <div className="flex items-center space-x-1">
                <i className="ri-luggage-cart-line text-xs"></i>
                <span>15 kg</span>
              </div>
            </div>
          </div>

          <div className="flex items-center space-x-3">
            {!compact && (
              <button
                onClick={() => setShowDetails(!showDetails)}
                className="text-[#013688] font-semibold hover:text-blue-700 transition-colors text-sm"
              >
                {showDetails ? 'Hide Details' : 'Flight Details'}
              </button>
            )}
            {compact && selected && (
              <div className="flex items-center space-x-1 text-[#013688] font-medium text-sm">
                <i className="ri-check-line"></i>
                <span>Selected</span>
              </div>
            )}
            <button
              onClick={handleBookNow}
              className="bg-[#013688] text-white px-6 py-2 rounded-lg font-semibold hover:bg-blue-700 transition-all text-sm"
            >
              Book Now
            </button>
          </div>
        </div>

        {/* Expanded Details with Tabs */}
        {showDetails && !compact && (
          <div className="mt-6 pt-6 border-t border-gray-100">
            {/* Tab Navigation */}
            <div className="border-b border-gray-200 mb-6">
              <nav className="flex">
                <button
                  onClick={() => setActiveDetailsTab('flight')}
                  className={`flex-1 py-3 px-4 text-sm font-medium border-b-2 transition-colors ${
                    activeDetailsTab === 'flight'
                      ? 'border-[#013688] text-[#013688] bg-blue-50'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  Flight Information
                </button>
                <button
                  onClick={() => setActiveDetailsTab('fare')}
                  className={`flex-1 py-3 px-4 text-sm font-medium border-b-2 transition-colors ${
                    activeDetailsTab === 'fare'
                      ? 'border-[#013688] text-[#013688] bg-blue-50'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  Fare Summary & Rules
                </button>
                <button
                  onClick={() => setActiveDetailsTab('baggage')}
                  className={`flex-1 py-3 px-4 text-sm font-medium border-b-2 transition-colors ${
                    activeDetailsTab === 'baggage'
                      ? 'border-[#013688] text-[#013688] bg-blue-50'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  Baggage Information
                </button>
              </nav>
            </div>

            {/* Tab Content */}
            <div className="min-h-[200px]">
              {/* Flight Information Tab */}
              {activeDetailsTab === 'flight' && (
                <div className="space-y-6">
                  {/* Route Header */}
                  <div className="text-lg font-semibold text-gray-900 flex items-center space-x-2">
                    <span>{firstSegment.DepartureAirport?.city} → {lastSegment.ArrivalAirport?.city}</span>
                  </div>

                  {/* Flight Details for Each Segment */}
                  {segments.map((segment: any, index: number) => (
                    <div key={index} className="bg-white border border-gray-200 rounded-lg p-6">
                      {/* Airline Info Header */}
                      <div className="flex items-center justify-between mb-6">
                        <div className="flex items-center space-x-3">
                          <img
                            src={getAirlineLogo(segment.FlightDesignator?.MAC?.code)}
                            alt={segment.FlightDesignator?.MAC?.name || 'Airline'}
                            className="w-12 h-8 object-contain"
                            onError={handleImageError}
                          />
                          <div>
                            <div className="font-semibold text-gray-900 text-lg">
                              {segment.FlightDesignator?.MAC?.name || 'Unknown Airline'}
                            </div>
                            <div className="text-sm text-gray-600">
                              {segment.FlightDesignator?.FlightNumber || 'N/A'}
                            </div>
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="text-sm text-gray-600">Aircraft</div>
                          <div className="font-medium">{segment.Equipment || 'Airbus A320'}</div>
                          <div className="text-sm text-gray-600 mt-2">Travel Class</div>
                          <div className="font-medium">Economy</div>
                        </div>
                      </div>

                      {/* Flight Timeline */}
                      <div className="flex items-start justify-between">
                        {/* Departure */}
                        <div className="text-left flex-1">
                          <div className="text-3xl font-bold text-gray-900 mb-1">
                            {formatTime(segment.DepartureTime)}
                          </div>
                          <div className="font-medium text-gray-900 mb-1">
                            {segment.DepartureAirport?.city} [{segment.DepartureAirport?.code}]
                          </div>
                          <div className="text-sm text-gray-600 mb-1">
                            {segment.DepartureAirport?.name}
                          </div>
                          <div className="text-sm text-gray-600">
                            Terminal {segment.DepartureTerminal || 'N/A'}
                          </div>
                        </div>

                        {/* Duration */}
                        <div className="flex-1 text-center px-6 py-4">
                          <div className="text-sm text-gray-600 mb-2">
                            {formatDuration(segment.duration || 0)}
                          </div>
                          <div className="flex items-center justify-center mb-2">
                            <div className="flex-1 border-t-2 border-dotted border-gray-300"></div>
                            <div className="mx-3">
                              <i className="ri-plane-line text-[#013688] text-2xl"></i>
                            </div>
                            <div className="flex-1 border-t-2 border-dotted border-gray-300"></div>
                          </div>
                          <div className="text-sm text-gray-600">0</div>
                        </div>

                        {/* Arrival */}
                        <div className="text-right flex-1">
                          <div className="text-3xl font-bold text-gray-900 mb-1">
                            {formatTime(segment.Arrivaltime)}
                          </div>
                          <div className="font-medium text-gray-900 mb-1">
                            {segment.ArrivalAirport?.city} [{segment.ArrivalAirport?.code}]
                          </div>
                          <div className="text-sm text-gray-600 mb-1">
                            {segment.ArrivalAirport?.name}
                          </div>
                          <div className="text-sm text-gray-600">
                            Terminal {segment.ArrivalTerminal || 'N/A'}
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}

              {/* Fare Summary & Rules Tab */}
              {activeDetailsTab === 'fare' && (
                <div className="space-y-4">
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    {/* Left Column - Fare Rules */}
                    <div>
                      <div className="text-sm font-medium text-gray-900 mb-4">
                        {firstSegment.DepartureAirport?.city} - {lastSegment.ArrivalAirport?.city}
                      </div>
                      
                      <div className="bg-gray-50 rounded-lg overflow-hidden">
                        <div className="bg-gray-100 px-4 py-2 flex justify-between text-sm font-medium">
                          <span>CHANGES/REISSUE</span>
                          <span>Adult</span>
                        </div>
                        <div className="p-4 space-y-3">
                          <div className="flex justify-between text-sm">
                            <span className="text-gray-600">Before</span>
                            <span className="font-medium">₹ 3000</span>
                          </div>
                          <div className="flex justify-between text-sm">
                            <span className="text-gray-600">After</span>
                            <span className="font-medium">Non Changeable</span>
                          </div>
                        </div>
                      </div>

                      {/* Disclaimer */}
                      <div className="mt-6 space-y-3 text-xs text-gray-600">
                        <p>• The above data is indicatory, fare rules are subject to changes by the Airline.</p>
                        <p>• Cancellation/Date change request will be accepted 30 hrs prior to departure.</p>
                        <p>• GST + RAF charges applicable on cancellation/Reissue penalty.</p>
                      </div>
                    </div>

                    {/* Right Column - Fare Details */}
                    <div className="bg-gray-50 rounded-lg p-4">
                      <div className="flex justify-between items-center mb-4">
                        <h4 className="font-semibold text-gray-900">Fare Details</h4>
                        <span className="text-sm text-[#013688] font-medium">1 Traveller</span>
                      </div>
                      <div className="space-y-3 text-sm">
                        <div className="flex justify-between items-center">
                          <span className="text-gray-700">Base Fare</span>
                          <span className="font-medium">₹ {baseFare.toLocaleString()}</span>
                        </div>
                        <div className="flex justify-between items-center">
                          <span className="text-gray-700">Tax & Charges</span>
                          <span className="font-medium">₹ {taxes.toLocaleString()}</span>
                        </div>
                        <div className="border-t border-gray-300 pt-3 mt-4">
                          <div className="flex justify-between items-center">
                            <span className="font-semibold text-gray-900">Total Amount:</span>
                            <span className="font-bold text-lg">₹ {totalFare.toLocaleString()}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Baggage Information Tab */}
              {activeDetailsTab === 'baggage' && (
                <div className="space-y-6">
                  {/* Baggage Table */}
                  <div className="bg-white border border-gray-200 rounded-lg overflow-hidden">
                    <div className="grid grid-cols-3 bg-gray-50">
                      <div className="px-6 py-4 text-center font-medium text-gray-900 border-r border-gray-200">
                        Sector/Flight
                      </div>
                      <div className="px-6 py-4 text-center font-medium text-gray-900 border-r border-gray-200">
                        Check in Baggage
                      </div>
                      <div className="px-6 py-4 text-center font-medium text-gray-900">
                        Cabin Baggage
                      </div>
                    </div>
                    <div className="grid grid-cols-3 border-t border-gray-200">
                      <div className="px-6 py-4 text-center text-gray-900 border-r border-gray-200">
                        {firstSegment.DepartureAirport?.city} - {lastSegment.ArrivalAirport?.city}
                      </div>
                      <div className="px-6 py-4 text-center text-gray-900 border-r border-gray-200">
                        15 Kg (Adult)
                      </div>
                      <div className="px-6 py-4 text-center text-gray-900">
                        7 Kg (Adult)
                      </div>
                    </div>
                  </div>

                  {/* Baggage Information Notes */}
                  <div className="space-y-4 text-sm text-gray-600">
                    <p>• The information presented above is as obtained from the airline reservation system.</p>
                    <p>• The baggage allowance may vary according to stop-overs, connecting flights and changes in airline rules.</p>
                  </div>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Show flight details button in compact view */}
        {compact && (
          <div className="text-end mt-2">
            <button
              onClick={handleShowFlightDetails}
              className="text-[#013688] underline text-sm"
            >
              Flight Details
            </button>
          </div>
        )}
      </div>

      {/* Right-to-Left Flight Details Popup */}
      {showFlightDetails && (
        <div className="fixed inset-0 bg-black/50 z-[200] flex items-center justify-end">
          <div className={`bg-white h-full w-full max-w-lg shadow-2xl transform transition-transform duration-300 overflow-y-auto ${showFlightDetails ? 'translate-x-0' : 'translate-x-full'}`}>
            {/* Header */}
            <div className="sticky top-0 bg-white border-b border-gray-200 px-6 py-4 flex items-center justify-between">
              <h2 className="text-lg font-bold text-gray-900">Domestic Flight Details</h2>
              <button
                onClick={handleCloseFlightDetails}
                className="p-2 hover:bg-gray-100 rounded-full transition-colors"
              >
                <i className="ri-close-line text-xl text-gray-500"></i>
              </button>
            </div>

            {/* Flight Summary */}
            <div className="p-4 border-b border-gray-100 bg-gray-50">
              <div className="flex items-center space-x-3 mb-3">
                <img 
                  src={getAirlineLogo(firstSegment.FlightDesignator?.MAC?.code)} 
                  alt={firstSegment.FlightDesignator?.MAC?.name || 'Airline'} 
                  className="w-12 h-8 object-contain"
                  onError={handleImageError}
                />
                <div>
                  <div className="font-semibold text-gray-900">
                    {firstSegment.FlightDesignator?.MAC?.name || 'Unknown Airline'}
                  </div>
                  <div className="text-sm text-gray-500">
                    {firstSegment.FlightDesignator?.FlightNumber || 'N/A'}
                  </div>
                </div>
              </div>
              <div className="flex items-center justify-between text-sm">
                <div>
                  <div className="font-bold text-lg">{formatTime(firstSegment.DepartureTime)}</div>
                  <div className="text-gray-600">
                    {firstSegment.DepartureAirport?.city} ({firstSegment.DepartureAirport?.code})
                  </div>
                </div>
                <div className="text-center px-4">
                  <div className="text-gray-600 mb-1">{formatDuration(totalDuration)}</div>
                  <div className="flex items-center space-x-2">
                    <div className="w-2 h-2 bg-[#013688] rounded-full"></div>
                    <div className="flex-1 h-0.5 bg-gray-300 relative min-w-[60px]">
                      <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                        <i className="ri-plane-line text-[#013688]"></i>
                      </div>
                    </div>
                    <div className="w-2 h-2 bg-[#013688] rounded-full"></div>
                  </div>
                  <div className="text-xs text-gray-500 mt-1">
                    {segments.length > 1 ? `${segments.length - 1} stop${segments.length > 2 ? 's' : ''}` : 'Non-stop'}
                  </div>
                </div>
                <div className="text-right">
                  <div className="font-bold text-lg">{formatTime(lastSegment.Arrivaltime)}</div>
                  <div className="text-gray-600">
                    {lastSegment.ArrivalAirport?.city} ({lastSegment.ArrivalAirport?.code})
                  </div>
                </div>
              </div>
            </div>

            {/* Tabs */}
            <div className="border-b border-gray-200">
              <nav className="flex">
                <button
                  onClick={() => setActiveTab('details')}
                  className={`flex-1 py-3 px-4 text-sm font-medium border-b-2 transition-colors ${activeTab === 'details' ? 'border-[#013688] text-[#013688] bg-blue-50' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'}`}
                >
                  Flight Details
                </button>
                <button
                  onClick={() => setActiveTab('baggage')}
                  className={`flex-1 py-3 px-4 text-sm font-medium border-b-2 transition-colors ${activeTab === 'baggage' ? 'border-[#013688] text-[#013688] bg-blue-50' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'}`}
                >
                  Baggage
                </button>
                <button
                  onClick={() => setActiveTab('fare')}
                  className={`flex-1 py-3 px-4 text-sm font-medium border-b-2 transition-colors ${activeTab === 'fare' ? 'border-[#013688] text-[#013688] bg-blue-50' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'}`}
                >
                  Fare & Rules
                </button>
              </nav>
            </div>

            {/* Tab Content */}
            <div className="p-6">
              {activeTab === 'details' && (
                <div className="space-y-4">
                  <h3 className="font-semibold text-gray-900">Flight Information</h3>
                  {segments.map((segment: any, index: number) => (
                    <div key={index} className="bg-gray-50 rounded-lg p-4">
                      <div className="grid grid-cols-2 gap-4 text-sm">
                        <div>
                          <div className="text-gray-600">Route</div>
                          <div className="font-medium">
                            {segment.DepartureAirport?.city} → {segment.ArrivalAirport?.city}
                          </div>
                        </div>
                        <div>
                          <div className="text-gray-600">Flight Number</div>
                          <div className="font-medium">{segment.FlightDesignator?.FlightNumber}</div>
                        </div>
                        <div>
                          <div className="text-gray-600">Aircraft Type</div>
                          <div className="font-medium">{segment.Equipment || 'Airbus A320'}</div>
                        </div>
                        <div>
                          <div className="text-gray-600">Duration</div>
                          <div className="font-medium">{formatDuration(segment.duration || 0)}</div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}

              {activeTab === 'baggage' && (
                <div className="space-y-4">
                  <h3 className="font-semibold text-gray-900">Baggage Allowance</h3>
                  <div className="bg-blue-50 rounded-lg p-4">
                    <div className="text-sm space-y-2">
                      <div className="flex justify-between">
                        <span>Check-in Baggage:</span>
                        <span className="font-medium">15 kg</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Cabin Baggage:</span>
                        <span className="font-medium">7 kg</span>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {activeTab === 'fare' && (
                <div className="space-y-4">
                  <h3 className="font-semibold text-gray-900">Fare Breakdown</h3>
                  <div className="bg-gray-50 rounded-lg p-4">
                    <div className="space-y-3 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-600">Base Fare</span>
                        <span className="font-medium">₹{baseFare.toLocaleString()}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Tax & Charges</span>
                        <span className="font-medium">₹{taxes.toLocaleString()}</span>
                      </div>
                      <div className="border-t border-gray-300 pt-2 flex justify-between">
                        <span className="font-semibold text-gray-900">Total Amount</span>
                        <span className="font-bold text-lg text-[#013688]">₹{totalFare.toLocaleString()}</span>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}