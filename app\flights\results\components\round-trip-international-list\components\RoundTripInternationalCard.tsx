// 'use client';

// import React, { useState } from 'react';
// import { FlightOptionInternational } from '@/app/models/flight-list.model';
// import { Globe } from 'lucide-react';
// import { getAirlineLogo } from '@/app/utils/airlineUtils';

// interface RoundTripInternationalCardProps {
//   flight: FlightOptionInternational;
//   journeyType: 'onward' | 'return';
//   onSelect?: (flight: FlightOptionInternational) => void;
// }

// export default function RoundTripInternationalCard({ flight, journeyType, onSelect }: RoundTripInternationalCardProps) {
//   const [showDetails, setShowDetails] = useState(false);

//   // Extract flight data from the API structure
//   const segments = flight.sI || [];
//   const firstSegment = segments[0] || {};
//   const lastSegment = segments[segments.length - 1] || {};
//   const fareInfo = flight.totalPriceList?.[0] || {};

//   // Calculate total duration
//   const totalDuration = segments.reduce((total: number, segment: any) => {
//     const duration = segment.duration || 0;
//     return total + duration;
//   }, 0);

//   const formatDuration = (minutes: number) => {
//     const hours = Math.floor(minutes / 60);
//     const mins = minutes % 60;
//     return `${hours}h ${mins}m`;
//   };

//   const formatTime = (dateTime: string) => {
//     if (!dateTime) return '--:--';
//     try {
//       const date = new Date(dateTime);
//       return date.toLocaleTimeString('en-US', {
//         hour: '2-digit',
//         minute: '2-digit',
//         hour12: false
//       });
//     } catch {
//       return '--:--';
//     }
//   };

//   const formatDate = (dateTime: string) => {
//     if (!dateTime) return '';
//     try {
//       const date = new Date(dateTime);
//       return date.toLocaleDateString('en-US', {
//         month: 'short',
//         day: 'numeric'
//       });
//     } catch {
//       return '';
//     }
//   };

//   const handleImageError = (e: React.SyntheticEvent<HTMLImageElement>) => {
//     const target = e.target as HTMLImageElement;
//     target.src = '/AirlineLogo/All.png';
//   };

//   const journeyBadgeColor = journeyType === 'onward'
//     ? 'bg-blue-100 text-blue-800'
//     : 'bg-green-100 text-green-800';

//   const isMultiSegment = segments.length > 1;

//   // Get fare information
//   const adultFare = fareInfo.fd?.ADULT;
//   const totalFare = adultFare?.fC?.TF || 0;

//   return (
//     <div className="relative">
//       <div className="bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-all p-4">
//         {/* Journey Type Badge and International Indicator */}
//         <div className="flex justify-between items-start mb-3">
//           <div className="flex items-center space-x-2">
//             <span className={`px-2 py-1 rounded-full text-xs font-medium ${journeyBadgeColor}`}>
//               {journeyType === 'onward' ? 'Onward Journey' : 'Return Journey'}
//             </span>
//             <span className="px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800 flex items-center space-x-1">
//               <Globe className="w-3 h-3" />
//               <span>International</span>
//             </span>
//           </div>
//         </div>

//         {/* Main Flight Info - Compact Header */}
//         <div className="flex items-center justify-between mb-3">
//           <div className="flex items-center space-x-3">
//             <div className="flex-shrink-0">
//               <img
//                 src={getAirlineLogo(firstSegment.fD?.aI?.code)}
//                 alt={firstSegment.fD?.aI?.name || 'Airline'}
//                 className="w-10 h-6 object-contain"
//                 onError={handleImageError}
//               />
//             </div>
//             <div>
//               <div className="font-semibold text-gray-900 text-sm">
//                 {firstSegment.fD?.aI?.name || 'Unknown Airline'}
//               </div>
//               <div className="text-gray-500 text-xs">
//                 {isMultiSegment ? 'Multiple Airlines' : firstSegment.fD?.fN || 'N/A'}
//               </div>
//             </div>
//           </div>
//         </div>

//         {/* Flight Times and Duration - Compact Layout */}
//         <div className="grid grid-cols-5 gap-2 items-center mb-3">
//           {/* Departure */}
//           <div className="text-left">
//             <div className="font-bold text-gray-900 text-xl">{formatTime(firstSegment.dt)}</div>
//             <div className="text-gray-600 text-sm">{firstSegment.da?.city}</div>
//             <div className="text-gray-500 text-xs">{firstSegment.da?.code}</div>
//           </div>

//           {/* Duration and Route */}
//           <div className="col-span-3 text-center">
//             <div className="text-gray-600 text-sm mb-1">{formatDuration(totalDuration)}</div>
//             <div className="flex items-center justify-center mb-1">
//               <div className="w-2 h-2 bg-[#013688] rounded-full"></div>
//               <div className="flex-1 h-px bg-gray-300 mx-2 relative">
//                 <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
//                   <i className="ri-plane-line text-[#013688] text-sm"></i>
//                 </div>
//               </div>
//               <div className="w-2 h-2 bg-[#013688] rounded-full"></div>
//             </div>
//             <div className="text-gray-500 text-xs">
//               {isMultiSegment ? `${segments.length - 1} stop${segments.length > 2 ? 's' : ''}` : 'Non-stop'}
//             </div>
//           </div>

//           {/* Arrival */}
//           <div className="text-right">
//             <div className="font-bold text-gray-900 text-xl">{formatTime(lastSegment.at)}</div>
//             <div className="text-gray-600 text-sm">{lastSegment.aa?.city}</div>
//             <div className="text-gray-500 text-xs">{lastSegment.aa?.code}</div>
//           </div>
//         </div>

//         {/* Price and Action - Compact Bottom Section */}
//         <div className="flex items-center justify-between pt-3 border-t border-gray-100">
//           <div className="flex items-center space-x-4">
//             <div>
//               <div className="flex items-center space-x-2">
//                 <span className="font-bold text-gray-900 text-xl">₹{totalFare.toLocaleString()}</span>
//               </div>
//             </div>
//             <div className="text-sm text-gray-600">
//               <div className="flex items-center space-x-1">
//                 <i className="ri-luggage-cart-line text-xs"></i>
//                 <span>30 kg</span>
//               </div>
//             </div>
//           </div>

//           <div className="flex items-center space-x-3">
//             <button
//               onClick={() => setShowDetails(!showDetails)}
//               className="text-[#013688] font-semibold hover:text-blue-700 transition-colors text-sm"
//             >
//               {showDetails ? 'Hide Details' : 'Flight Details'}
//             </button>
//             <button
//               onClick={() => onSelect?.(flight)}
//               className="bg-[#013688] text-white px-4 py-2 rounded-lg font-semibold hover:bg-blue-700 transition-all text-sm"
//             >
//               Select
//             </button>
//           </div>
//         </div>

//       {/* Expandable Details */}
//       {showDetails && (
//         <div className="mt-4 pt-4 border-t border-gray-200">
//           <h4 className="font-semibold mb-2">Flight Details</h4>
//           {segments.map((segment: any, index: number) => (
//             <div key={index} className="mb-3 last:mb-0">
//               <div className="flex justify-between items-center">
//                 <div>
//                   <div className="font-medium">
//                     {segment.da?.city} → {segment.aa?.city}
//                   </div>
//                   <div className="text-sm text-gray-600">
//                     {segment.fD?.aI?.name} • {segment.fD?.fN}
//                   </div>
//                   <div className="text-xs text-gray-500">
//                     Aircraft: {segment.fD?.eT || 'N/A'}
//                   </div>
//                 </div>
//                 <div className="text-right text-sm">
//                   <div>{formatTime(segment.dt)} - {formatTime(segment.at)}</div>
//                   <div className="text-gray-500">{formatDuration(segment.duration || 0)}</div>
//                   <div className="text-xs text-gray-500">
//                     {formatDate(segment.dt)} - {formatDate(segment.at)}
//                   </div>
//                 </div>
//               </div>
//               {index < segments.length - 1 && (
//                 <div className="mt-2 text-xs text-orange-600">
//                   Layover at {segment.aa?.city}
//                 </div>
//               )}
//             </div>
//           ))}

//           {/* International Fare Rules */}
//           <div className="mt-3 pt-3 border-t border-gray-100">
//             <h5 className="font-medium text-sm mb-2">International Fare Rules</h5>
//             <ul className="text-xs text-gray-600 space-y-1">
//               <li>• Cancellation charges apply as per airline policy</li>
//               <li>• Date change allowed with charges</li>
//               <li>• No refund on no-show</li>
//               <li>• Baggage allowance: 30kg check-in + 7kg cabin</li>
//               <li>• Visa and passport required for international travel</li>
//               <li>• Check-in closes 3 hours before departure</li>
//             </ul>
//           </div>
//         </div>
//       )}
//       </div>
//     </div>
//   );
// }

'use client';

import React, { useState } from 'react';
import { FlightOptionInternational } from '@/app/models/flight-list.model';
import { getAirlineLogo } from '@/app/utils/airlineUtils';

interface RoundTripInternationalCardProps {
  flight: FlightOptionInternational;
  journeyType: 'onward' | 'return';
  currency?: string;
  compact?: boolean;
  selected?: boolean;
  onSelect?: (flight: FlightOptionInternational) => void;
}

export default function RoundTripInternationalCard({ 
  flight, 
  journeyType, 
  currency = 'INR',
  compact = false, 
  selected = false, 
  onSelect 
}: RoundTripInternationalCardProps) {
  const [showDetails, setShowDetails] = useState(false);
  const [showFlightDetails, setShowFlightDetails] = useState(false);
  const [activeTab, setActiveTab] = useState('details');
  const [activeDetailsTab, setActiveDetailsTab] = useState('flight');

  // Extract flight data from the API structure
  const segments = flight.sI || [];
  const firstSegment = segments[0] || {};
  const lastSegment = segments[segments.length - 1] || {};
  const fareInfo = flight.totalPriceList?.[0] || {};

  // Calculate total duration
  const totalDuration = segments.reduce((total: number, segment: any) => {
    const duration = segment.duration || 0;
    return total + duration;
  }, 0);

  const formatDuration = (minutes: number) => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return `${hours}h ${mins}m`;
  };

  const formatTime = (dateTime: string) => {
    if (!dateTime) return '--:--';
    try {
      const date = new Date(dateTime);
      return date.toLocaleTimeString('en-US', {
        hour: '2-digit',
        minute: '2-digit',
        hour12: false
      });
    } catch {
      return '--:--';
    }
  };

  const formatDate = (dateTime: string) => {
    if (!dateTime) return '';
    try {
      const date = new Date(dateTime);
      return date.toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric'
      });
    } catch {
      return '';
    }
  };

  const handleImageError = (e: React.SyntheticEvent<HTMLImageElement>) => {
    const target = e.target as HTMLImageElement;
    target.src = '/AirlineLogo/All.png';
  };

  const handleShowFlightDetails = () => {
    setShowFlightDetails(true);
    setActiveTab('details');
  };

  const handleCloseFlightDetails = () => {
    setShowFlightDetails(false);
  };

  const handleBookNow = () => {
    onSelect?.(flight);
  };

  const formatPrice = (price: number) => {
    return currency === 'USD' ? `$${price.toLocaleString()}` : `₹${price.toLocaleString()}`;
  };

  const journeyBadgeColor = journeyType === 'onward'
    ? 'bg-blue-100 text-blue-800'
    : 'bg-green-100 text-green-800';

  const isMultiSegment = segments.length > 1;
  const isInternational = firstSegment.da?.country !== lastSegment.aa?.country;

  // Get fare information
  const adultFare = fareInfo.fd?.ADULT;
  const totalFare = adultFare?.fC?.TF || 0;
  const baseFare = adultFare?.fC?.BF || 0;
  const taxes = adultFare?.fC?.TAF || 0;

  return (
    <div className="relative">
      <div className={`bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-all ${selected ? 'border-purple-500 bg-purple-50' : ''} ${compact ? 'p-3' : 'p-4'}`}>
        {/* Trip Type Badge */}
        <div className="mb-2">
          <div className="flex items-center justify-between">
            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
              Round Trip • International
            </span>
            <span className={`px-2 py-1 rounded-full text-xs font-medium ${journeyBadgeColor}`}>
              {journeyType === 'onward' ? 'Onward Journey' : 'Return Journey'}
            </span>
          </div>
        </div>

        {/* Main Flight Info - Compact Header */}
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center space-x-3">
            <div className="flex-shrink-0">
              <img
                src={getAirlineLogo(firstSegment.fD?.aI?.code)}
                alt={firstSegment.fD?.aI?.name || 'Airline'}
                className="w-10 h-6 object-contain"
                onError={handleImageError}
              />
            </div>
            <div>
              <div className="font-semibold text-gray-900 text-sm">
                {firstSegment.fD?.aI?.name || 'Unknown Airline'}
              </div>
              <div className="text-gray-500 text-xs">
                {isMultiSegment ? 'Multiple Airlines' : firstSegment.fD?.fN || 'N/A'}
              </div>
            </div>
          </div>
        </div>

        {/* Flight Times and Duration - Compact Layout */}
        <div className="grid grid-cols-5 gap-2 items-center mb-3">
          {/* Departure */}
          <div className="text-left">
            <div className="font-bold text-gray-900 text-xl">{formatTime(firstSegment.dt)}</div>
            <div className="text-gray-600 text-sm">{firstSegment.da?.city}</div>
            <div className="text-gray-500 text-xs">{firstSegment.da?.code}</div>
            <div className="text-gray-400 text-xs">{firstSegment.da?.country || 'IN'}</div>
          </div>

          {/* Duration and Route */}
          <div className="col-span-3 text-center">
            <div className="text-gray-600 text-sm mb-1">{formatDuration(totalDuration)}</div>
            <div className="flex items-center justify-center mb-1">
              <div className="w-2 h-2 bg-purple-600 rounded-full"></div>
              <div className="flex-1 h-px bg-gray-300 mx-2 relative">
                <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                  <i className={`${journeyType === 'return' ? 'ri-plane-line rotate-180' : 'ri-plane-line'} text-purple-600 text-sm`}></i>
                </div>
              </div>
              <div className="w-2 h-2 bg-purple-600 rounded-full"></div>
            </div>
            <div className="text-gray-500 text-xs">
              {isMultiSegment ? `${segments.length - 1} stop${segments.length > 2 ? 's' : ''}` : 'Non-stop'}
            </div>
            {isMultiSegment && (
              <div className="text-orange-600 text-xs mt-1">
                <i className="ri-time-line mr-1"></i>
                Layover
              </div>
            )}
          </div>

          {/* Arrival */}
          <div className="text-right">
            <div className="font-bold text-gray-900 text-xl">{formatTime(lastSegment.at)}</div>
            <div className="text-gray-600 text-sm">{lastSegment.aa?.city}</div>
            <div className="text-gray-500 text-xs">{lastSegment.aa?.code}</div>
            <div className="text-gray-400 text-xs">{lastSegment.aa?.country || 'US'}</div>
          </div>
        </div>

        {/* International-specific info */}
        {isInternational && (
          <div className="mb-3 pt-3 border-t border-gray-100">
            <div className="flex items-center justify-between text-xs text-gray-600">
              <div className="flex items-center space-x-4">
                <div className="flex items-center space-x-1">
                  <i className="ri-passport-line"></i>
                  <span>Visa Required</span>
                </div>
                <div className="flex items-center space-x-1">
                  <i className="ri-luggage-cart-line"></i>
                  <span>30kg</span>
                </div>
              </div>
              <div className="text-right">
                <div>Check-in: 3 hours before</div>
              </div>
            </div>
          </div>
        )}

        {/* Additional International Info */}
        <div className="bg-amber-50 border border-amber-200 rounded-lg p-3 mb-4">
          <div className="flex items-start space-x-2">
            <i className="ri-information-line text-amber-600 text-sm mt-0.5 flex-shrink-0"></i>
            <div className="text-sm text-amber-800">
              <div className="font-medium mb-1">International Travel Requirements</div>
              <div className="text-xs space-y-1">
                <div>• Valid passport required (6+ months validity)</div>
                <div>• Visa requirements vary by destination</div>
                <div>• Arrive 3 hours before international departure</div>
              </div>
            </div>
          </div>
        </div>

        {/* Price and Action - Compact Bottom Section */}
        <div className="flex items-center justify-between pt-3 border-t border-gray-100">
          <div className="flex items-center space-x-4">
            <div>
              <div className="flex items-center space-x-2">
                <span className="font-bold text-gray-900 text-xl">{formatPrice(totalFare)}</span>
                <span className="text-sm text-gray-500">per person</span>
              </div>
              {currency !== 'INR' && (
                <div className="text-xs text-gray-400">
                  ~₹{Math.round(totalFare * 83).toLocaleString()} (approx)
                </div>
              )}
            </div>
            <div className="text-sm text-gray-600">
              <div className="flex items-center space-x-1">
                <i className="ri-luggage-cart-line text-xs"></i>
                <span>30 kg</span>
              </div>
            </div>
          </div>

          <div className="flex items-center space-x-3">
            {!compact && (
              <button
                onClick={() => setShowDetails(!showDetails)}
                className="text-purple-600 font-semibold hover:text-purple-700 transition-colors text-sm"
              >
                {showDetails ? 'Hide Details' : 'View Details'}
              </button>
            )}
            {compact && selected && (
              <div className="flex items-center space-x-1 text-purple-600 font-medium text-sm">
                <i className="ri-check-line"></i>
                <span>Selected</span>
              </div>
            )}
            <button
              onClick={handleBookNow}
              className="bg-purple-600 text-white px-6 py-2 rounded-lg font-semibold hover:bg-purple-700 transition-all text-sm"
            >
              Select
            </button>
          </div>
        </div>

        {/* Expanded Details with Tabs */}
        {showDetails && !compact && (
          <div className="mt-6 pt-6 border-t border-gray-100">
            {/* Tab Navigation */}
            <div className="border-b border-gray-200 mb-6">
              <nav className="flex">
                <button
                  onClick={() => setActiveDetailsTab('flight')}
                  className={`flex-1 py-3 px-4 text-sm font-medium border-b-2 transition-colors ${
                    activeDetailsTab === 'flight'
                      ? 'border-purple-500 text-purple-600 bg-purple-50'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  Flight Information
                </button>
                <button
                  onClick={() => setActiveDetailsTab('fare')}
                  className={`flex-1 py-3 px-4 text-sm font-medium border-b-2 transition-colors ${
                    activeDetailsTab === 'fare'
                      ? 'border-purple-500 text-purple-600 bg-purple-50'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  Fare Summary & Rules
                </button>
                <button
                  onClick={() => setActiveDetailsTab('travel')}
                  className={`flex-1 py-3 px-4 text-sm font-medium border-b-2 transition-colors ${
                    activeDetailsTab === 'travel'
                      ? 'border-purple-500 text-purple-600 bg-purple-50'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  Travel Requirements
                </button>
              </nav>
            </div>

            {/* Tab Content */}
            <div className="min-h-[200px]">
              {/* Flight Information Tab */}
              {activeDetailsTab === 'flight' && (
                <div className="space-y-6">
                  {/* Route Header */}
                  <div className="text-lg font-semibold text-gray-900 flex items-center space-x-2">
                    <span>{firstSegment.da?.city} → {lastSegment.aa?.city}</span>
                    <span className="text-sm font-normal text-gray-600">
                      ({journeyType} Journey)
                    </span>
                  </div>

                  {/* Flight Details for Each Segment */}
                  {segments.map((segment: any, index: number) => (
                    <div key={index} className="bg-white border border-gray-200 rounded-lg p-6">
                      {/* Airline Info Header */}
                      <div className="flex items-center justify-between mb-6">
                        <div className="flex items-center space-x-3">
                          <img
                            src={getAirlineLogo(segment.fD?.aI?.code)}
                            alt={segment.fD?.aI?.name || 'Airline'}
                            className="w-12 h-8 object-contain"
                            onError={handleImageError}
                          />
                          <div>
                            <div className="font-semibold text-gray-900 text-lg">
                              {segment.fD?.aI?.name || 'Unknown Airline'}
                            </div>
                            <div className="text-sm text-gray-600">
                              {segment.fD?.fN || 'N/A'}
                            </div>
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="text-sm text-gray-600">Aircraft</div>
                          <div className="font-medium">{segment.fD?.eT || 'Boeing 777-300ER'}</div>
                          <div className="text-sm text-gray-600 mt-2">Travel Class</div>
                          <div className="font-medium">Economy</div>
                        </div>
                      </div>

                      {/* Flight Timeline */}
                      <div className="flex items-start justify-between">
                        {/* Departure */}
                        <div className="text-left flex-1">
                          <div className="text-3xl font-bold text-gray-900 mb-1">
                            {formatTime(segment.dt)}
                          </div>
                          <div className="text-sm text-gray-600 mb-1">{formatDate(segment.dt)}</div>
                          <div className="font-medium text-gray-900 mb-1">
                            {segment.da?.city} [{segment.da?.code}]
                          </div>
                          <div className="text-sm text-gray-600 mb-1">
                            {segment.da?.name}
                          </div>
                          <div className="text-sm text-gray-600">
                            Terminal {segment.da?.terminal || 'N/A'}
                          </div>
                        </div>

                        {/* Duration */}
                        <div className="flex-1 text-center px-6 py-4">
                          <div className="text-sm text-gray-600 mb-2">
                            {formatDuration(segment.duration || 0)}
                          </div>
                          <div className="flex items-center justify-center mb-2">
                            <div className="flex-1 border-t-2 border-dotted border-gray-300"></div>
                            <div className="mx-3">
                              <i className="ri-plane-line text-purple-600 text-2xl"></i>
                            </div>
                            <div className="flex-1 border-t-2 border-dotted border-gray-300"></div>
                          </div>
                          <div className="text-sm text-gray-600">0</div>
                        </div>

                        {/* Arrival */}
                        <div className="text-right flex-1">
                          <div className="text-3xl font-bold text-gray-900 mb-1">
                            {formatTime(segment.at)}
                          </div>
                          <div className="text-sm text-gray-600 mb-1">{formatDate(segment.at)}</div>
                          <div className="font-medium text-gray-900 mb-1">
                            {segment.aa?.city} [{segment.aa?.code}]
                          </div>
                          <div className="text-sm text-gray-600 mb-1">
                            {segment.aa?.name}
                          </div>
                          <div className="text-sm text-gray-600">
                            Terminal {segment.aa?.terminal || 'N/A'}
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}

              {/* Fare Summary & Rules Tab */}
              {activeDetailsTab === 'fare' && (
                <div className="space-y-4">
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    {/* Fare Rules for International */}
                    <div className="bg-red-50 rounded-lg p-4">
                      <h4 className="font-semibold text-red-800 mb-3">International Cancellation Policy</h4>
                      <div className="text-sm text-red-700 space-y-2">
                        <div className="flex justify-between">
                          <span>More than 15 days</span>
                          <span className="font-medium">₹7,500 + taxes</span>
                        </div>
                        <div className="flex justify-between">
                          <span>7-15 days</span>
                          <span className="font-medium">₹12,000 + taxes</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Less than 7 days</span>
                          <span className="font-medium">Non-refundable</span>
                        </div>
                      </div>
                    </div>

                    {/* Fare Details */}
                    <div className="bg-gray-50 rounded-lg p-4">
                      <div className="flex justify-between items-center mb-4">
                        <h4 className="font-semibold text-gray-900">Fare Details</h4>
                        <span className="text-sm text-purple-600 font-medium">1 Traveller</span>
                      </div>
                      <div className="space-y-3 text-sm">
                        <div className="flex justify-between items-center">
                          <span className="text-gray-700">Base Fare</span>
                          <span className="font-medium">{formatPrice(baseFare)}</span>
                        </div>
                        <div className="flex justify-between items-center">
                          <span className="text-gray-700">Tax & Charges</span>
                          <span className="font-medium">{formatPrice(taxes)}</span>
                        </div>
                        <div className="border-t border-gray-300 pt-3 mt-4">
                          <div className="flex justify-between items-center">
                            <span className="font-semibold text-gray-900">Total Amount:</span>
                            <span className="font-bold text-lg">{formatPrice(totalFare)}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Travel Requirements Tab */}
              {activeDetailsTab === 'travel' && (
                <div className="space-y-4">
                  <div className="bg-amber-50 rounded-lg p-4">
                    <h4 className="font-semibold text-amber-800 mb-3">Travel Requirements</h4>
                    <div className="text-sm text-amber-700 space-y-2">
                      <div>• Passport valid for 6+ months</div>
                      <div>• Visa required for destination country</div>
                      <div>• Health insurance recommended</div>
                      <div>• COVID-19 vaccination certificate may be required</div>
                      <div>• Arrive 3 hours before international departure</div>
                    </div>
                  </div>
                  
                  <div className="bg-green-50 rounded-lg p-4">
                    <h4 className="font-semibold text-green-800 mb-3">Included Services</h4>
                    <div className="text-sm text-green-700 space-y-1">
                      <div>✓ In-flight meals and beverages</div>
                      <div>✓ Entertainment system</div>
                      <div>✓ Blanket and pillow</div>
                      <div>✓ Priority check-in available</div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Show flight details button in compact view */}
        {compact && (
          <div className="text-end mt-2">
            <button
              onClick={handleShowFlightDetails}
              className="text-purple-600 underline text-sm"
            >
              Flight Details
            </button>
          </div>
        )}
      </div>

      {/* Right-to-Left Flight Details Popup */}
      {showFlightDetails && (
        <div className="fixed inset-0 bg-black/50 z-[200] flex items-center justify-end">
          <div className={`bg-white h-full w-full max-w-lg shadow-2xl transform transition-transform duration-300 overflow-y-auto ${showFlightDetails ? 'translate-x-0' : 'translate-x-full'}`}>
            {/* Header */}
            <div className="sticky top-0 bg-white border-b border-gray-200 px-6 py-4 flex items-center justify-between">
              <div>
                <h2 className="text-lg font-bold text-gray-900">International Flight Details</h2>
                <p className="text-sm text-gray-600">{journeyType} Journey</p>
              </div>
              <button
                onClick={handleCloseFlightDetails}
                className="p-2 hover:bg-gray-100 rounded-full transition-colors"
              >
                <i className="ri-close-line text-xl text-gray-500"></i>
              </button>
            </div>

            {/* Flight Summary */}
            <div className="p-4 border-b border-gray-100 bg-gray-50">
              <div className="flex items-center space-x-3 mb-3">
                <img 
                  src={getAirlineLogo(firstSegment.fD?.aI?.code)} 
                  alt={firstSegment.fD?.aI?.name || 'Airline'} 
                  className="w-12 h-8 object-contain"
                  onError={handleImageError}
                />
                <div>
                  <div className="font-semibold text-gray-900">
                    {firstSegment.fD?.aI?.name || 'Unknown Airline'}
                  </div>
                  <div className="text-sm text-gray-500">
                    {firstSegment.fD?.fN || 'N/A'}
                  </div>
                </div>
              </div>
              <div className="flex items-center justify-between text-sm">
                <div>
                  <div className="font-bold text-lg">{formatTime(firstSegment.dt)}</div>
                  <div className="text-gray-600">
                    {firstSegment.da?.city} ({firstSegment.da?.code})
                  </div>
                </div>
                <div className="text-center px-4">
                  <div className="text-gray-600 mb-1">{formatDuration(totalDuration)}</div>
                  <div className="flex items-center space-x-2">
                    <div className="w-2 h-2 bg-purple-600 rounded-full"></div>
                    <div className="flex-1 h-0.5 bg-gray-300 relative min-w-[60px]">
                      <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                        <i className="ri-plane-line text-purple-600"></i>
                      </div>
                    </div>
                    <div className="w-2 h-2 bg-purple-600 rounded-full"></div>
                  </div>
                  <div className="text-xs text-gray-500 mt-1">
                    {isMultiSegment ? `${segments.length - 1} stop${segments.length > 2 ? 's' : ''}` : 'Non-stop'}
                  </div>
                </div>
                <div className="text-right">
                  <div className="font-bold text-lg">{formatTime(lastSegment.at)}</div>
                  <div className="text-gray-600">
                    {lastSegment.aa?.city} ({lastSegment.aa?.code})
                  </div>
                </div>
              </div>
            </div>

            {/* Tabs */}
            <div className="border-b border-gray-200">
              <nav className="flex">
                <button
                  onClick={() => setActiveTab('details')}
                  className={`flex-1 py-3 px-4 text-sm font-medium border-b-2 transition-colors ${activeTab === 'details' ? 'border-purple-500 text-purple-600 bg-purple-50' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'}`}
                >
                  Flight Info
                </button>
                <button
                  onClick={() => setActiveTab('baggage')}
                  className={`flex-1 py-3 px-4 text-sm font-medium border-b-2 transition-colors ${activeTab === 'baggage' ? 'border-purple-500 text-purple-600 bg-purple-50' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'}`}
                >
                  Baggage
                </button>
                <button
                  onClick={() => setActiveTab('travel')}
                  className={`flex-1 py-3 px-4 text-sm font-medium border-b-2 transition-colors ${activeTab === 'travel' ? 'border-purple-500 text-purple-600 bg-purple-50' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'}`}
                >
                  Travel Info
                </button>
              </nav>
            </div>

            {/* Tab Content */}
            <div className="p-6">
              {activeTab === 'details' && (
                <div className="space-y-4">
                  <h3 className="font-semibold text-gray-900">Flight Information</h3>
                  {segments.map((segment: any, index: number) => (
                    <div key={index} className="bg-gray-50 rounded-lg p-4">
                      <div className="grid grid-cols-2 gap-4 text-sm">
                        <div>
                          <div className="text-gray-600">Route</div>
                          <div className="font-medium">
                            {segment.da?.city} → {segment.aa?.city}
                          </div>
                        </div>
                        <div>
                          <div className="text-gray-600">Flight Number</div>
                          <div className="font-medium">{segment.fD?.fN}</div>
                        </div>
                        <div>
                          <div className="text-gray-600">Aircraft Type</div>
                          <div className="font-medium">{segment.fD?.eT || 'Boeing 777-300ER'}</div>
                        </div>
                        <div>
                          <div className="text-gray-600">Duration</div>
                          <div className="font-medium">{formatDuration(segment.duration || 0)}</div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}

{activeTab === 'baggage' && (
                <div className="space-y-4">
                  <h3 className="font-semibold text-gray-900">International Baggage Allowance</h3>
                  <div className="bg-blue-50 rounded-lg p-4">
                    <div className="text-sm space-y-3">
                      <div className="flex justify-between">
                        <span>Check-in Baggage:</span>
                        <span className="font-medium">30 kg (2 pieces)</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Cabin Baggage:</span>
                        <span className="font-medium">7 kg + personal item</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Excess Baggage:</span>
                        <span className="font-medium">$150 per kg</span>
                      </div>
                    </div>
                  </div>
                  
                  <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                    <div className="font-medium text-red-800 mb-2">
                      <i className="ri-information-line mr-2"></i>
                      Important Notice
                    </div>
                    <div className="text-sm text-red-700">
                      Pre-book excess baggage online to save up to 50% on airport charges. International flights have stricter baggage policies.
                    </div>
                  </div>
                </div>
              )}

              {activeTab === 'travel' && (
                <div className="space-y-4">
                  <div className="bg-amber-50 rounded-lg p-4">
                    <h4 className="font-semibold text-amber-800 mb-3">Travel Requirements</h4>
                    <div className="text-sm text-amber-700 space-y-2">
                      <div>• Passport valid for 6+ months</div>
                      <div>• Visa required for destination country</div>
                      <div>• Health insurance recommended</div>
                      <div>• COVID-19 vaccination certificate may be required</div>
                      <div>• Arrive 3 hours before international departure</div>
                    </div>
                  </div>
                  
                  <div className="bg-green-50 rounded-lg p-4">
                    <h4 className="font-semibold text-green-800 mb-3">Included Services</h4>
                    <div className="text-sm text-green-700 space-y-1">
                      <div>✓ In-flight meals and beverages</div>
                      <div>✓ Entertainment system</div>
                      <div>✓ Blanket and pillow</div>
                      <div>✓ Priority check-in available</div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}